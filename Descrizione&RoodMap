🛡️ VITA – La tua salvezza, nascosta in piena vista.
🔐 App di emergenza invisibile per situazioni reali di pericolo
VITA è un’applicazione progettata per proteggere chi si trova in situazioni di pericolo (stalking, violenza domestica, rapimenti, minacce) permettendo di chiedere aiuto senza attirare l’attenzione.

Dietro un’interfaccia fittizia – come una calcolatrice o un blocco note – si nasconde un sistema di sicurezza completo, attivabile in modo segreto tramite parole vocali, tocchi speciali o codici.

🧩 INTERFACCIA FANTASMA
Al primo avvio, l’utente sceglie una delle interfacce mascherate (calcolatrice, note, meteo, calendario).

L’accesso alla vera app è possibile solo tramite:

Inserimento di un codice segreto (es. 7347)

Pronuncia di una parola chiave decisa dall’utente (es. “mamma”)

5 tocchi consecutivi in un punto qualsiasi dello schermo (con lo schermo acceso)

🚨 MODALITÀ EMERGENZA “SOS”
Una volta attivata, la modalità SOS può:

Inviare la posizione in tempo reale ai contatti fidati (fino a 4)

Iniziare una registrazione audio ambientale o inviare vocali pre-registrati

Attivare una modalità invisibile che resta attiva anche con schermo spento

Inviare notifiche discrete come “Tutto ok – segui i movimenti” al contatto SOS

Fare upload criptato automatico sul cloud (opzionale)

🎙️ ATTIVAZIONE VOCALE INVISIBILE
L’utente registra una o più parole chiave neutre (es. “mamma”, “cena”, “luna”)

Quando una di queste viene pronunciata, anche casualmente durante una chiamata o conversazione, l’app entra automaticamente in modalità SOS

Il sistema riconosce anche il tono di voce per distinguere un uso normale da un uso in emergenza (opzionale)

✋ MODALITÀ TATTILE SOS
Se l’utente non può parlare, può:

Toccare 5 volte di seguito in un punto qualsiasi dello schermo (anche sulla calcolatrice finta)

Attivare l’allarme silenzioso istantaneamente

📦 FUNZIONI EXTRA SICUREZZA
Check-in automatico: se l’utente non risponde a un promemoria entro X minuti, parte il piano SOS

Modalità viaggio sicuro: se l’utente non arriva a destinazione entro un certo orario → SOS

Auto-distruzione: possibilità di cancellare dati sensibili in 1 secondo, con gesture o PIN

Cloud criptato (opzionale): backup automatico delle prove raccolte (posizione/audio)

📋 DATI CONFIGURABILI DALL’UTENTE
Nome e cognome

Data di nascita

Indirizzo di residenza

Numeri di emergenza (fino a 4 contatti fidati)

Vocali personalizzati per comunicare con i soccorsi (es. “Non posso parlare”)

Codice di accesso all’app

Parole chiave vocali

Frequenza dei check-in automatici

🧠 TECNOLOGIA UTILIZZATA
🔒 Criptazione end-to-end AES-256 per tutti i dati sensibili

🎙️ Riconoscimento vocale offline con whisper.cpp o Picovoice Porcupine

📍 GPS preciso + fallback su WiFi triangolazione

📱 App realizzata in Flutter, compatibile con Android e iOS

☁️ Integrazione con Firebase per cloud storage criptato e notifiche push (opzionale)

🎯 DESTINATARI
Persone vittime di stalking o violenza domestica

Donne e uomini che vivono in ambienti pericolosi

Ragazzi e ragazze che rientrano tardi la sera

Famiglie che vogliono proteggere membri vulnerabili

Chiunque voglia uno strumento silenzioso di sicurezza personale

🧭 PROSSIMI STEP DI SVILUPPO
MVP con:

Interfaccia fittizia

Codice segreto

Modalità SOS base (GPS + contatti)

Attivazione con tocchi

Integrazione riconoscimento vocale

Registrazioni vocali e modalità viaggio

Cloud sicuro e auto-distruzione

Versione Pro con AI personalizzata per tono voce



🛠️ ROADMAP TECNICA DI SVILUPPO – VITA (v1.0 MVP + Upgrade)
🌱 FASE 1 – MVP BASE (Stealth App + Codice segreto + SOS manuale)
🔧 Task di sviluppo:
 Creazione progetto Flutter (multi-piattaforma Android/iOS)

 Scelta interfaccia fittizia iniziale (Calcolatrice)

 Creazione maschera interfaccia reale con form campi:

Nome, cognome, data nascita, residenza, numeri di emergenza

 Creazione sistema di accesso via codice segreto numerico

 Modalità SOS manuale:

 Invia posizione

 Invia SMS / notifica a 1-4 contatti fidati

⚙️ Prompt Engineering (internal AI logic):

PROMPT → Se viene inserita la sequenza "XXXX", attiva interfaccia emergenza. 
Se utente clicca 5 volte consecutive in meno di 3 secondi in qualsiasi punto dello schermo, attiva modalità SOS.


🔉 FASE 2 – INTEGRAZIONE RICONOSCIMENTO VOCALE OFFLINE (Porcupine / Whisper.cpp)
🔧 Task di sviluppo:
 Integrazione libreria di riconoscimento vocale offline:

Picovoice Porcupine (hotword detection)

Whisper.cpp per frasi complete

 Registrazione parola chiave segreta da parte dell’utente

 Attivazione modalità SOS quando la parola viene rilevata

⚙️ Prompt AI (hotword listener logic):
PROMPT → Ascolta in background la parola chiave definita dall'utente. 
Se viene rilevata, attiva la funzione "safeSOS()".
🎤 FASE 3 – REGISTRAZIONI VOCALI PERSONALIZZATE
🔧 Task di sviluppo:
 Interfaccia per registrare 1+ vocali

 Vocali usati in emergenza (es. per rispondere al telefono o inviare audio a contatti SOS)

 Associazione tra hotword → vocale → azione

 Storage criptato dei vocali in locale

⚙️ Prompt AI (condizionamento audio-vocale):
nginx
Copia
Modifica
PROMPT → Se viene detta la parola "mamma" in tono agitato, invia il vocale “Non posso parlare ora” e attiva safeSOS().
Se tono è neutro → nessuna azione.
📍 FASE 4 – GEOLOCALIZZAZIONE E PIANO SOS AUTOMATICO
🔧 Task di sviluppo:
 Integrazione GPS

 Monitoraggio posizione in background

 Impostazione “Percorso sicuro” (da luogo A a luogo B)

 Timer di sicurezza: se utente non conferma arrivo entro X → SOS

⚙️ Prompt AI (sorveglianza geocontestuale):
scss
Copia
Modifica
PROMPT → Se la posizione utente non cambia da [posizione iniziale] dopo 30 minuti e non ha cliccato “tutto ok”, attiva safeSOS().
🔒 FASE 5 – SICUREZZA & AUTO-DISTRUZIONE
🔧 Task di sviluppo:
 Aggiunta PIN separato per apertura app vera

 Sistema autodistruzione dati (wipe criptato)

 Timeout anti-sequestro (dopo X minuti app blocca tutto)

⚙️ Prompt AI (autoprotezione):
css
Copia
Modifica
PROMPT → Se il PIN errato viene inserito 3 volte o se l’utente clicca “fake unlock”, elimina tutti i dati sensibili.
☁️ FASE 6 – CLOUD CRIPTATO + DASHBOARD CONTATTI
🔧 Task di sviluppo:
 Integrazione Firebase Storage

 Upload audio/posizione in tempo reale

 Notifiche push ai contatti SOS

 Mini-dashboard web o mobile per i contatti

⚙️ Prompt AI (upload + dispatch):
nginx
Copia
Modifica
PROMPT → Quando viene attivato SOS, carica su cloud criptato: posizione, audio ambientale, timestamp, info utente. Invia link criptato ai contatti di emergenza.
🚀 FASE 7 – VERSIONE PRO (AI personalizzata + tono vocale + fake call + live stream)
🔧 Feature avanzate:
 Analisi tono vocale AI (in tempo reale)

 Attivazione solo se parola + tono coincidenti

 Simulazione finta chiamata (“Mamma”, “Amica”)

 Streaming video d’urgenza (10s cam anteriore)

⚙️ Prompt complessi:
scss
Copia
Modifica
PROMPT → Se la parola chiave viene detta con tono di panico (basato su spettrogramma vocale), attiva “panicMode()”.
Se “chiama mamma” viene detto due volte in 10s, attiva fakeCall().
🎯 STRUTTURA COMPLETA DEI COMANDI AI (esempi)
Evento	Prompt Interno	Azione
Parola chiave rilevata	"mamma"	Attiva safeSOS()
5 tocchi rapidi	tapSequence() == 5 in 3s	safeSOS()
Percorso non completato	!checkInDone() entro tempo limite	safeSOS()
PIN errato 3x	pinFails == 3	wipeData()
Tono vocale anomalo	toneAnalysis().panicScore > 0.8	panicMode()
Vocal trigger audio	"Non posso parlare"	sendAudioToContacts()

🧩 STRUMENTI CONSIGLIATI
Componente	Tecnologia
App	Flutter
Storage criptato	SQLCipher o Hive + AES
GPS + background	Geolocator / location
Notifiche	Firebase Cloud Messaging
Riconoscimento vocale	Whisper.cpp / Porcupine
Backend	Firebase / Supabase (opzionale)

